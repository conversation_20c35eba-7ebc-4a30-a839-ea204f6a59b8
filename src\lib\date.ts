import {
  format,
  parseISO,
  isAfter,
  isBefore,
  isWithinInterval,
  differenceInYears,
  startOfDay,
  endOfDay,
  isValid,
  compareAsc,
} from 'date-fns'

/**
 * Date utilities for the Philippine enrollment system
 * Handles academic year dates, enrollment periods, and student age calculations
 */

// Types for academic year date operations
export interface AcademicYearDates {
  startDate: string
  endDate: string
  enrollmentStartDate: string
  enrollmentEndDate: string
}

/**
 * Creates academic year dates object from individual date components
 * @param startDate - Academic year start date (ISO string)
 * @param endDate - Academic year end date (ISO string)
 * @param enrollmentStartDate - Enrollment period start date (ISO string)
 * @param enrollmentEndDate - Enrollment period end date (ISO string)
 * @returns Object with academic year dates
 */
export const createAcademicYearDates = (
  startDate: string,
  endDate: string,
  enrollmentStartDate: string,
  enrollmentEndDate: string
): AcademicYearDates => {
  return {
    startDate,
    endDate,
    enrollmentStartDate,
    enrollmentEndDate,
  }
}

/**
 * Validates school year format and logic
 * @param schoolYear - Format: "YYYY-YYYY" (e.g., "2024-2025")
 * @returns Boolean indicating if school year format is valid
 */
export const validateSchoolYearFormat = (schoolYear: string): boolean => {
  if (!schoolYear.match(/^\d{4}-\d{4}$/)) {
    return false
  }

  const [startYearStr, endYearStr] = schoolYear.split('-')
  const startYear = parseInt(startYearStr)
  const endYear = parseInt(endYearStr)

  return endYear === startYear + 1
}

/**
 * Formats a date string for display in the Philippine context
 * @param dateString - ISO date string
 * @param formatStyle - Format style ('short', 'medium', 'long')
 * @returns Formatted date string
 */
export const formatAcademicDate = (
  dateString: string,
  formatStyle: 'short' | 'medium' | 'long' = 'medium'
): string => {
  try {
    const date = parseISO(dateString)

    if (!isValid(date)) {
      return 'Invalid Date'
    }

    switch (formatStyle) {
      case 'short':
        return format(date, 'MMM d, yyyy') // Jan 15, 2024
      case 'medium':
        return format(date, 'MMMM d, yyyy') // January 15, 2024
      case 'long':
        return format(date, 'EEEE, MMMM d, yyyy') // Monday, January 15, 2024
      default:
        return format(date, 'MMMM d, yyyy')
    }
  } catch (error) {
    console.error('Error formatting date:', error)
    return 'Invalid Date'
  }
}

/**
 * Checks if the current date is within the enrollment period
 * @param enrollmentStartDate - ISO date string
 * @param enrollmentEndDate - ISO date string
 * @param referenceDate - Optional reference date (defaults to current date)
 * @returns Boolean indicating if enrollment is currently open
 */
export const isEnrollmentPeriodActive = (
  enrollmentStartDate: string,
  enrollmentEndDate: string,
  referenceDate?: Date
): boolean => {
  try {
    const startDate = parseISO(enrollmentStartDate)
    const endDate = parseISO(enrollmentEndDate)
    const checkDate = referenceDate || new Date()

    if (!isValid(startDate) || !isValid(endDate)) {
      return false
    }

    return isWithinInterval(checkDate, {
      start: startOfDay(startDate),
      end: endOfDay(endDate),
    })
  } catch (error) {
    console.error('Error checking enrollment period:', error)
    return false
  }
}

/**
 * Validates academic year date ranges with flexible logic
 * @param dates - Academic year dates object
 * @returns Boolean indicating if all date ranges are valid
 */
export const validateAcademicYearDateRanges = (dates: AcademicYearDates): boolean => {
  try {
    const startDate = parseISO(dates.startDate)
    const endDate = parseISO(dates.endDate)
    const enrollmentStart = parseISO(dates.enrollmentStartDate)
    const enrollmentEnd = parseISO(dates.enrollmentEndDate)

    // Check if all dates are valid
    if (
      !isValid(startDate) ||
      !isValid(endDate) ||
      !isValid(enrollmentStart) ||
      !isValid(enrollmentEnd)
    ) {
      return false
    }

    // Academic year: start date must be before end date
    if (!isBefore(startDate, endDate)) {
      return false
    }

    // Enrollment period: start date must be before end date
    if (!isBefore(enrollmentStart, enrollmentEnd)) {
      return false
    }

    // Flexible validation: Allow enrollment periods that extend beyond academic year end
    // This accommodates various calendar patterns and regional variations
    // Only require that enrollment doesn't start more than 2 years before academic year starts
    // and doesn't end more than 1 year after academic year ends
    const twoYearsBefore = new Date(startDate)
    twoYearsBefore.setFullYear(twoYearsBefore.getFullYear() - 2)

    const oneYearAfter = new Date(endDate)
    oneYearAfter.setFullYear(oneYearAfter.getFullYear() + 1)

    if (isBefore(enrollmentStart, twoYearsBefore) || isAfter(enrollmentEnd, oneYearAfter)) {
      return false
    }

    return true
  } catch (error) {
    console.error('Error validating academic year dates:', error)
    return false
  }
}

/**
 * Calculates a student's age based on birth date
 * @param birthDate - ISO date string
 * @param referenceDate - Optional reference date (defaults to current date)
 * @returns Age in years
 */
export const calculateStudentAge = (birthDate: string, referenceDate?: string): number => {
  try {
    const birth = parseISO(birthDate)
    const reference = referenceDate ? parseISO(referenceDate) : new Date()

    if (!isValid(birth) || !isValid(reference)) {
      throw new Error('Invalid date provided')
    }

    return differenceInYears(reference, birth)
  } catch (error) {
    console.error('Error calculating student age:', error)
    return 0
  }
}

/**
 * Validates if a student's age is appropriate for their grade level
 * @param birthDate - ISO date string
 * @param gradeLevel - Grade level (7-12)
 * @param referenceDate - Optional reference date (defaults to current date)
 * @returns Boolean indicating if age is within acceptable range
 */
export const validateStudentAge = (
  birthDate: string,
  gradeLevel: number,
  referenceDate?: string
): boolean => {
  try {
    const age = calculateStudentAge(birthDate, referenceDate)

    // Typical age ranges for Philippine high school
    const expectedAge = gradeLevel + 5 // Grade 7 = ~12 years old

    // Allow 2 years younger to 3 years older than expected
    return age >= expectedAge - 2 && age <= expectedAge + 3
  } catch (error) {
    console.error('Error validating student age:', error)
    return false
  }
}

/**
 * Determines the current academic year based on the current date and available academic years
 * @param academicYears - Array of academic year objects
 * @param referenceDate - Optional reference date (defaults to current date)
 * @returns The current academic year or null if none found
 */
export const getCurrentAcademicYear = <T extends { startDate: string; endDate: string }>(
  academicYears: T[],
  referenceDate?: Date
): T | null => {
  try {
    const checkDate = referenceDate || new Date()

    for (const academicYear of academicYears) {
      const startDate = parseISO(academicYear.startDate)
      const endDate = parseISO(academicYear.endDate)

      if (!isValid(startDate) || !isValid(endDate)) {
        continue
      }

      if (
        isWithinInterval(checkDate, {
          start: startOfDay(startDate),
          end: endOfDay(endDate),
        })
      ) {
        return academicYear
      }
    }

    return null
  } catch (error) {
    console.error('Error determining current academic year:', error)
    return null
  }
}

/**
 * Sorts academic years by start date (most recent first)
 * @param academicYears - Array of academic year objects
 * @returns Sorted array of academic years
 */
export const sortAcademicYearsByDate = <T extends { startDate: string }>(
  academicYears: T[]
): T[] => {
  return [...academicYears].sort((a, b) => {
    try {
      const dateA = parseISO(a.startDate)
      const dateB = parseISO(b.startDate)

      if (!isValid(dateA) || !isValid(dateB)) {
        return 0
      }

      // Sort in descending order (most recent first)
      return compareAsc(dateB, dateA)
    } catch (error) {
      console.error('Error sorting academic years:', error)
      return 0
    }
  })
}

/**
 * Checks if a date string is a valid ISO date
 * @param dateString - Date string to validate
 * @returns Boolean indicating if the date is valid
 */
export const isValidISODate = (dateString: string): boolean => {
  try {
    const date = parseISO(dateString)
    return isValid(date)
  } catch {
    return false
  }
}

/**
 * Calculates the duration of an academic year in days
 * @param startDate - Academic year start date (ISO string)
 * @param endDate - Academic year end date (ISO string)
 * @returns Number of days in the academic year, or 0 if invalid dates
 */
export const calculateAcademicYearDuration = (startDate: string, endDate: string): number => {
  try {
    const start = parseISO(startDate)
    const end = parseISO(endDate)

    if (!isValid(start) || !isValid(end) || !isBefore(start, end)) {
      return 0
    }

    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
  } catch (error) {
    console.error('Error calculating academic year duration:', error)
    return 0
  }
}

/**
 * Calculates the duration of an enrollment period in days
 * @param enrollmentStartDate - Enrollment start date (ISO string)
 * @param enrollmentEndDate - Enrollment end date (ISO string)
 * @returns Number of days in the enrollment period, or 0 if invalid dates
 */
export const calculateEnrollmentPeriodDuration = (
  enrollmentStartDate: string,
  enrollmentEndDate: string
): number => {
  try {
    const start = parseISO(enrollmentStartDate)
    const end = parseISO(enrollmentEndDate)

    if (!isValid(start) || !isValid(end) || !isBefore(start, end)) {
      return 0
    }

    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
  } catch (error) {
    console.error('Error calculating enrollment period duration:', error)
    return 0
  }
}

/**
 * Checks if a given date falls within the academic year period
 * @param checkDate - Date to check (ISO string or Date object)
 * @param academicYearDates - Academic year dates object
 * @returns Boolean indicating if the date is within the academic year
 */
export const isDateWithinAcademicYear = (
  checkDate: string | Date,
  academicYearDates: AcademicYearDates
): boolean => {
  try {
    const date = typeof checkDate === 'string' ? parseISO(checkDate) : checkDate
    const startDate = parseISO(academicYearDates.startDate)
    const endDate = parseISO(academicYearDates.endDate)

    if (!isValid(date) || !isValid(startDate) || !isValid(endDate)) {
      return false
    }

    return isWithinInterval(date, {
      start: startOfDay(startDate),
      end: endOfDay(endDate),
    })
  } catch (error) {
    console.error('Error checking if date is within academic year:', error)
    return false
  }
}

/**
 * Gets the relative position of a date within an academic year (0.0 to 1.0)
 * @param checkDate - Date to check (ISO string or Date object)
 * @param academicYearDates - Academic year dates object
 * @returns Number between 0.0 and 1.0 representing position in academic year, or -1 if invalid
 */
export const getAcademicYearProgress = (
  checkDate: string | Date,
  academicYearDates: AcademicYearDates
): number => {
  try {
    const date = typeof checkDate === 'string' ? parseISO(checkDate) : checkDate
    const startDate = parseISO(academicYearDates.startDate)
    const endDate = parseISO(academicYearDates.endDate)

    if (!isValid(date) || !isValid(startDate) || !isValid(endDate)) {
      return -1
    }

    const totalDuration = endDate.getTime() - startDate.getTime()
    const elapsed = date.getTime() - startDate.getTime()

    if (elapsed < 0) return 0 // Before academic year starts
    if (elapsed > totalDuration) return 1 // After academic year ends

    return elapsed / totalDuration
  } catch (error) {
    console.error('Error calculating academic year progress:', error)
    return -1
  }
}
