import type { Doc, Id } from 'convex/_generated/dataModel'

// Convex Document Types
export type AcademicYearDoc = Doc<'academicYears'>
export type AcademicYearId = Id<'academicYears'>
export type AcademicYearInput = Omit<AcademicYearDoc, '_id' | '_creationTime'>

export type Track = Doc<'tracks'>
export type TrackId = Id<'tracks'>
export type TrackInput = Omit<Track, '_id' | '_creationTime'>

export type Strand = Doc<'strands'>
export type StrandId = Id<'strands'>
export type StrandInput = Omit<Strand, '_id' | '_creationTime'>

export type Major = Doc<'majors'>
export type MajorId = Id<'majors'>
export type MajorInput = Omit<Major, '_id' | '_creationTime'>

export type StudentDoc = Doc<'students'>
export type StudentId = Id<'students'>
export type StudentInput = Omit<StudentDoc, '_id' | '_creationTime'>

export type TeacherDoc = Doc<'teachers'>
export type TeacherId = Id<'teachers'>
export type TeacherInput = Omit<TeacherDoc, '_id' | '_creationTime'>

export type Section = Doc<'sections'>
export type SectionId = Id<'sections'>
export type SectionInput = Omit<Section, '_id' | '_creationTime'>

export type EnrollmentDoc = Doc<'enrollments'>
export type EnrollmentId = Id<'enrollments'>
export type EnrollmentInput = Omit<EnrollmentDoc, '_id' | '_creationTime'>

export type User = Doc<'users'>
export type UserId = Id<'users'>
export type UserInput = Omit<User, '_id' | '_creationTime'>
