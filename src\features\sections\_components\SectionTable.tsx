import * as React from 'react'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  type SortingState,
} from '@tanstack/react-table'
import {
  MoreHorizontalIcon,
  EditIcon,
  Trash2Icon,
  UsersIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ArrowUpDownIcon,
} from 'lucide-react'
import { useQuery } from 'convex/react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/Table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu'
import { api } from 'convex/_generated/api'
import type { Section, SectionId } from '@/lib/types'

interface SectionTableProps {
  gradeLevel: number | null
  onEdit: (sectionId: SectionId) => void
  onDelete: (sectionId: SectionId) => void
}

const columnHelper = createColumnHelper<Section>()

export function SectionTable({ gradeLevel, onEdit, onDelete }: SectionTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])

  const allSections = useQuery(api.sections.getAll)
  const sections = allSections
    ? allSections.filter((section) => {
        const matchesGradeLevel = gradeLevel === null || section.gradeLevel === gradeLevel
        return matchesGradeLevel && section.isActive
      })
    : []

  const columns = [
    columnHelper.accessor('name', {
      header: 'Section Name',
      cell: (info) => <div className="font-medium">{info.getValue()}</div>,
    }),
    columnHelper.accessor('gradeLevel', {
      header: 'Grade Level',
      cell: (info) => <Badge variant="outline">Grade {info.getValue()}</Badge>,
    }),
    columnHelper.display({
      id: 'adviser',
      header: 'Adviser',
      cell: () => <div className="text-sm text-muted-foreground">Loading...</div>,
    }),
    columnHelper.display({
      id: 'track',
      header: 'Track',
      cell: (info) => {
        const gradeLevel = info.row.original.gradeLevel

        if (gradeLevel <= 10) {
          return <span className="text-muted-foreground text-sm">N/A (Junior High)</span>
        }

        return <div className="text-sm text-muted-foreground">Loading...</div>
      },
    }),
    columnHelper.display({
      id: 'strand',
      header: 'Strand',
      cell: (info) => {
        const gradeLevel = info.row.original.gradeLevel

        if (gradeLevel <= 10) {
          return <span className="text-muted-foreground text-sm">N/A (Junior High)</span>
        }

        return <div className="text-sm text-muted-foreground">Loading...</div>
      },
    }),
    columnHelper.display({
      id: 'enrollment',
      header: 'Enrollment',
      cell: (info) => {
        const section = info.row.original
        const total = section.maleCount + section.femaleCount

        return (
          <div className="text-sm">
            <div className="font-medium">Total: {total}</div>
            <div className="text-muted-foreground">
              Male: {section.maleCount}, Female: {section.femaleCount}
            </div>
          </div>
        )
      },
    }),
    columnHelper.display({
      id: 'actions',
      header: '',
      cell: (info) => {
        const section = info.row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontalIcon />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(section._id)}>
                <EditIcon />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDelete(section._id)} variant="destructive">
                <Trash2Icon />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    }),
  ]

  const table = useReactTable({
    data: sections,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  if (sections.length === 0) {
    return (
      <div className="p-8 text-center">
        <UsersIcon className="mx-auto text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No sections found</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          No sections match your current filters. Try adjusting your search criteria.
        </p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {header.isPlaceholder ? null : (
                    <div
                      className={
                        header.column.getCanSort()
                          ? 'cursor-pointer select-none flex items-center gap-2'
                          : ''
                      }
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {header.column.getCanSort() &&
                        (header.column.getIsSorted() === 'asc' ? (
                          <ArrowUpIcon />
                        ) : header.column.getIsSorted() === 'desc' ? (
                          <ArrowDownIcon />
                        ) : (
                          <ArrowUpDownIcon />
                        ))}
                    </div>
                  )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
