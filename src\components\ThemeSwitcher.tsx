import * as React from 'react'
import { SunIcon, MoonStarIcon } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useThemeStore } from '@/stores/themeStore'
import { cn } from '@/lib/utils'

interface ThemeSwitcherProps {
  className?: string
  size?: 'default' | 'sm' | 'lg' | 'icon'
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
}

export function ThemeSwitcher({
  className,
  size = 'icon',
  variant = 'link',
  ...props
}: ThemeSwitcherProps) {
  const { theme, resolvedTheme, setTheme } = useThemeStore()
  const [isToggling, setIsToggling] = React.useState(false)

  const handleToggle = () => {
    setIsToggling(true)
    if (theme === 'system') {
      setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')
    } else {
      setTheme(theme === 'light' ? 'dark' : 'light')
    }
    setTimeout(() => setIsToggling(false), 300)
  }

  const isDark = resolvedTheme === 'dark'
  const currentTheme = theme === 'system' ? `System (${resolvedTheme})` : theme

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleToggle}
      className={cn(
        'transition-all duration-300 ease-in-out',
        'hover:scale-105 active:scale-95',
        'focus-visible:ring-2 focus-visible:ring-offset-2',
        isDark ? 'focus-visible:ring-purple-500' : 'focus-visible:ring-amber-500',
        className
      )}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
      title={`Current theme: ${currentTheme}. Click to switch to ${
        isDark ? 'light' : 'dark'
      } mode.`}
      {...props}
    >
      {isDark ? (
        <MoonStarIcon
          className={cn(
            'transition-all duration-300 ease-in-out',
            'text-purple-500',
            isToggling && 'rotate-180 scale-110',
            'hover:brightness-110'
          )}
        />
      ) : (
        <SunIcon
          className={cn(
            'transition-all duration-300 ease-in-out',
            'text-amber-500',
            isToggling && 'rotate-180 scale-110',
            'hover:brightness-110'
          )}
        />
      )}
      <span className="sr-only">{isDark ? 'Switch to light mode' : 'Switch to dark mode'}</span>
    </Button>
  )
}
