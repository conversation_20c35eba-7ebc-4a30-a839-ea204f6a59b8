import { createFileRoute, Link } from '@tanstack/react-router'
import { Button } from '@/components/ui/Button'
import { Authenticated, Unauthenticated, AuthLoading } from 'convex/react'
import { Spinner } from '@/components/ui/Spinner'
import { ThemeSwitcher } from '@/components/ThemeSwitcher'
import { useAuthActions } from '@convex-dev/auth/react'
import { useState } from 'react'
import { toast } from 'sonner'

export const Route = createFileRoute('/')({
  head: () => ({
    meta: [
      { title: 'Enrollr' },
      {
        name: 'description',
        content: 'A modern SaaS application built with Vite, React, and Convex',
      },
      { name: 'keywords', content: 'saas, vite, react, convex, web app, modern development' },
      { name: 'robots', content: 'index,follow' },
    ],
    links: [{ rel: 'canonical', href: 'http://localhost:5173/' }],
  }),
  component: Index,
})

function Index() {
  const { signIn } = useAuthActions()
  const [isSigningIn, setIsSigningIn] = useState(false)

  const handleGoogleSignIn = async () => {
    try {
      setIsSigningIn(true)
      await signIn('google', { redirectTo: '/dashboard' })
    } catch {
      toast.error('Sign in failed. Please try again.')
    } finally {
      setIsSigningIn(false)
    }
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <img src="/vite.svg" alt="Logo" className="size-8" />
              <h1 className="text-xl font-bold text-foreground">Enrollr</h1>
            </div>
            <div className="flex items-center space-x-3">
              <ThemeSwitcher />
            </div>
          </div>
        </div>
      </header>
      <main className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center space-y-8 py-12">
          <div className="space-y-6">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight">
              Welcome to <span className="text-primary">Enrollr</span>
            </h1>
            <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              A modern enrollment management platform built with Vite, React, and Convex. Secure
              authentication powered by Google OAuth.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
            <AuthLoading>
              <Spinner variant="circle" />
            </AuthLoading>
            <Authenticated>
              <Button asChild size="lg">
                <Link to="/dashboard">Dashboard</Link>
              </Button>
            </Authenticated>
            <Unauthenticated>
              <Button asChild size="lg">
                <Link to="/dashboard">Get Started</Link>
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={handleGoogleSignIn}
                disabled={isSigningIn}
              >
                {isSigningIn ? (
                  <>
                    <Spinner variant="circle" />
                    Signing in...
                  </>
                ) : (
                  <>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      className="mr-2"
                    >
                      <path
                        fill="#4285F4"
                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      />
                      <path
                        fill="#34A853"
                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      />
                      <path
                        fill="#FBBC05"
                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      />
                      <path
                        fill="#EA4335"
                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      />
                    </svg>
                    Continue with Google
                  </>
                )}
              </Button>
            </Unauthenticated>
          </div>
        </div>
      </main>
      <footer className="border-t bg-card/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-16">
            <p className="text-sm text-muted-foreground">
              © 2025 Enrollr. Built with modern web technologies.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
